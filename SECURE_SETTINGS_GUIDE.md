# 🔐 Secure Settings Management System

## Overview

The Secure Settings Management System provides a secure, encrypted way to manage API credentials and service configurations for Ocean Soul Sparkles without requiring manual `.env` file updates or Vercel redeployments.

## 🎯 Key Features

### ✅ **Database-First Configuration**
- All settings stored in encrypted `admin_settings` table
- No need to update `.env.production` or redeploy to Vercel
- Real-time configuration updates without downtime

### ✅ **Military-Grade Security**
- AES-256-CBC encryption for sensitive credentials
- Masked display in admin interface
- Role-based access control (Admin/Dev only)

### ✅ **Service Testing & Validation**
- Built-in credential testing before saving
- Real email sending tests
- Connection verification for all services

### ✅ **Multiple Email Service Support**
- Gmail SMTP with App Passwords
- Google Workspace SMTP
- OneSignal email service
- Automatic fallback between services

## 🚀 Quick Start

### 1. **Run Database Setup**
```sql
-- Copy and paste CRITICAL_PRODUCTION_FIX.sql into Supabase SQL Editor
-- This creates the admin_settings table and encryption functions
```

### 2. **Access Secure Settings**
1. Login to admin panel
2. Click "Admin Settings" in sidebar
3. Navigate to "🔐 Secure Settings"

### 3. **Configure Email Services**
Choose one or more email services to configure:

#### **Gmail SMTP (Recommended)**
- **Gmail Email Address**: Your Gmail account
- **Gmail App Password**: Generate in Google Account settings
- **From Email**: Optional custom from address
- **From Name**: "Ocean Soul Sparkles"

#### **Google Workspace SMTP**
- **Workspace Email**: Your workspace email
- **Workspace App Password**: Generate in Google Workspace
- **From Email**: Optional custom from address
- **From Name**: "Ocean Soul Sparkles"

#### **OneSignal**
- **OneSignal App ID**: From OneSignal dashboard
- **OneSignal REST API Key**: From OneSignal dashboard

### 4. **Test Configuration**
- Click "Test Service" for each configured service
- Use "Test & Save" to verify before saving
- Monitor test results in the interface

## 📋 Configuration Guide

### **Gmail SMTP Setup**

1. **Enable 2-Factor Authentication** on your Gmail account
2. **Generate App Password**:
   - Go to Google Account settings
   - Security → 2-Step Verification → App passwords
   - Generate password for "Mail"
3. **Configure in Secure Settings**:
   - Gmail Email: `<EMAIL>`
   - Gmail App Password: `xxxx xxxx xxxx xxxx`

### **Google Workspace Setup**

1. **Enable 2-Factor Authentication** on your Workspace account
2. **Generate App Password**:
   - Go to Google Account settings
   - Security → 2-Step Verification → App passwords
   - Generate password for "Mail"
3. **Configure in Secure Settings**:
   - Workspace Email: `<EMAIL>`
   - Workspace App Password: `xxxx xxxx xxxx xxxx`

### **OneSignal Setup**

1. **Create OneSignal Account** at onesignal.com
2. **Create App** for email notifications
3. **Get Credentials**:
   - App ID: From app settings
   - REST API Key: From app settings → Keys & IDs
4. **Configure in Secure Settings**:
   - OneSignal App ID: `xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx`
   - OneSignal REST API Key: `Basic xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx`

## 🔧 Advanced Configuration

### **Service Priority**
Configure which email service to try first:
- **Gmail → Workspace → OneSignal**: Try Gmail first, fallback to others
- **Workspace → Gmail → OneSignal**: Try Workspace first
- **OneSignal Only**: Use only OneSignal

### **Email Configuration**
- **Email System Enabled**: Master switch for all email functionality
- **From Name**: Default sender name for all emails

## 🧪 Testing & Validation

### **Individual Service Testing**
1. Configure service credentials
2. Click "Test Service" button
3. Check test results:
   - ✅ **Working**: Service configured correctly
   - ❌ **Failed**: Check credentials and try again

### **Complete Flow Testing**
1. Use "Test & Save" button
2. System tests all configured services
3. Sends test email to your admin email address
4. Verifies end-to-end functionality

### **Production Testing**
Test the Artist/Braider onboarding flow:
1. Create test user with Artist/Braider role
2. Check "Send welcome email"
3. Verify email is received with application link
4. Test token validation and application form

## 🔒 Security Features

### **Encryption**
- **Algorithm**: AES-256-CBC
- **Key Management**: Environment-based encryption key
- **Automatic**: Sensitive fields encrypted automatically

### **Access Control**
- **Admin/Dev Only**: Only admin and developer roles can access
- **Audit Trail**: All changes logged with user information
- **Session Security**: Requires active admin session

### **Data Protection**
- **Masked Display**: Sensitive values shown as `****xxxx****`
- **Secure Storage**: Encrypted in database
- **No Logs**: Sensitive values not logged in plain text

## 🚨 Troubleshooting

### **Common Issues**

#### **"Gmail SMTP Test Failed"**
- ✅ Check 2-Factor Authentication is enabled
- ✅ Verify App Password is correct (16 characters)
- ✅ Ensure "Less secure app access" is disabled
- ✅ Try generating new App Password

#### **"OneSignal Test Failed"**
- ✅ Verify App ID format: `xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx`
- ✅ Check REST API Key starts with "Basic "
- ✅ Ensure OneSignal app is configured for email

#### **"Settings Not Saving"**
- ✅ Check admin role permissions
- ✅ Verify database connection
- ✅ Check browser console for errors

### **Email Not Sending**
1. **Check Service Status**: Test each configured service
2. **Verify Priority**: Ensure at least one service is working
3. **Check Logs**: Monitor console for email sending logs
4. **Test Flow**: Use debug tools to test complete flow

## 📊 Monitoring & Maintenance

### **Regular Checks**
- **Monthly**: Test all configured email services
- **Quarterly**: Rotate App Passwords for security
- **As Needed**: Update service priorities based on reliability

### **Performance Monitoring**
- Monitor email delivery success rates
- Check service response times
- Review error logs for patterns

### **Security Maintenance**
- Regularly update encryption keys
- Monitor access logs
- Review and rotate API credentials

## 🔗 Integration with Existing Systems

### **Artist/Braider Onboarding**
- Automatically uses secure settings for welcome emails
- Fallback support ensures email delivery
- Token-based application links work seamlessly

### **General Email System**
- All system emails use secure settings
- Booking confirmations, notifications, etc.
- Consistent sender information across all emails

### **Future Integrations**
- Payment notifications
- Marketing campaigns
- Customer communications

## 📞 Support

### **Getting Help**
- Check this guide first
- Test individual services
- Review console logs
- Contact system administrator

### **Emergency Procedures**
If email system fails completely:
1. Check all service configurations
2. Test each service individually
3. Verify database connectivity
4. Fall back to environment variables if needed

---

**🎉 Congratulations!** You now have a secure, maintainable email configuration system that eliminates the need for manual environment variable updates and Vercel redeployments.

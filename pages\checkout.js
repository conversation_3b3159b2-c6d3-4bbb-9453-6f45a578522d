import { useState, useEffect } from 'react';
import Head from 'next/head';
import Link from 'next/link';
import { useRouter } from 'next/router';
import styles from '@/styles/Checkout.module.css';
import Layout from '@/components/Layout';
import AnimatedSection from '@/components/AnimatedSection';
import CustomerForm from '@/components/CustomerForm';
import { useCustomer } from '@/contexts/CustomerContext';

export default function Checkout() {
  const router = useRouter();
  const [cart, setCart] = useState([]);
  const { customer, saveGuestCustomer, loading: customerLoading } = useCustomer();
  const [customerStep, setCustomerStep] = useState(true); // true = customer info, false = payment
  const [orderSummary, setOrderSummary] = useState({
    subtotal: 0,
    shipping: 10.00,
    total: 0,
  });

  // Initialize cart from localStorage on component mount
  useEffect(() => {
    const savedCart = localStorage.getItem('cart');
    if (savedCart) {
      try {
        const parsedCart = JSON.parse(savedCart);
        setCart(parsedCart);

        // Calculate order summary
        const subtotal = parsedCart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
        const shipping = subtotal > 100 ? 0 : 10.00; // Free shipping for orders over $100

        setOrderSummary({
          subtotal,
          shipping,
          total: subtotal + shipping,
        });
      } catch (error) {
        console.error('Error parsing cart from localStorage:', error);
      }
    } else {
      // Redirect to shop if cart is empty
      router.push('/shop');
    }
  }, [router]);

  // Handle customer form completion
  const handleCustomerFormComplete = (customerData) => {
    console.log('Customer form completed with data:', customerData);
    setCustomerStep(false); // Proceed to payment step
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!customer) {
      // If no customer data, show error or return to customer step
      setCustomerStep(true);
      return;
    }

    try {
      // Process the order
      const orderData = {
        customer_id: customer.id,
        items: cart,
        subtotal: orderSummary.subtotal,
        shipping: orderSummary.shipping,
        total: orderSummary.total,
        status: 'PENDING'
      };

      const response = await fetch('/api/public/orders', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(orderData),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Error processing order');
      }

      // Clear cart
      localStorage.removeItem('cart');

      // Redirect to confirmation page
      router.push({
        pathname: '/confirmation',
        query: {
          orderId: result.order.id,
          status: 'COMPLETED'
        }
      });
    } catch (error) {
      console.error('Order submission error:', error);
      alert('There was an error processing your order. Please try again.');
    }
  };

  return (
    <Layout>
      <Head>
        <title>Checkout | OceanSoulSparkles</title>
        <meta name="description" content="Complete your purchase from OceanSoulSparkles. Secure checkout with multiple payment options." />
      </Head>

      <main className={styles.main}>
        <section className={styles.checkoutSection}>
          <AnimatedSection animation="fade-up">
            <h1 className={styles.pageTitle}>Checkout</h1>

            <div className={styles.checkoutContainer}>
              <div className={styles.checkoutForm}>
                {customerStep ? (
                  <div className={styles.customerFormContainer}>
                    <h2 className={styles.sectionTitle}>Customer Information</h2>
                    <CustomerForm 
                      isCheckout={true}
                      onComplete={handleCustomerFormComplete}
                    />
                  </div>
                ) : (
                  <form onSubmit={handleSubmit}>
                    <div className={styles.formSection}>
                      <h2 className={styles.sectionTitle}>Shipping Information</h2>
                      
                      {customer && (
                        <div className={styles.customerSummary}>
                          <h3>Customer Details</h3>
                          <p><strong>Name:</strong> {customer.name}</p>
                          <p><strong>Email:</strong> {customer.email}</p>
                          <p><strong>Phone:</strong> {customer.phone}</p>
                          {customer.address && (
                            <div className={styles.addressDetails}>
                              <p><strong>Address:</strong> {customer.address}</p>
                              <p><strong>City:</strong> {customer.city}, {customer.state} {customer.postal_code}</p>
                              <p><strong>Country:</strong> {customer.country}</p>
                            </div>
                          )}
                          <button 
                            type="button" 
                            className={styles.editButton}
                            onClick={() => setCustomerStep(true)}
                          >
                            Edit Information
                          </button>
                        </div>
                      )}
                    </div>

                    <div className={styles.formSection}>
                      <h2 className={styles.sectionTitle}>Payment</h2>

                      <div className={styles.paymentInfo}>
                        <p>We accept payments via Square:</p>
                        <div className={styles.paymentLogos}>
                          <img src="/images/logos/square.png" alt="Square Payments" width={120} height={30} />
                        </div>

                        <button type="submit" className={styles.checkoutButton}>
                          Complete Order
                        </button>

                        <div className={styles.securePaymentNote}>
                          <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M8 0C5.243 0 3 2.243 3 5V6H2C1.447 6 1 6.447 1 7V15C1 15.553 1.447 16 2 16H14C14.553 16 15 15.553 15 15V7C15 6.447 14.553 6 14 6H13V5C13 2.243 10.757 0 8 0ZM5 5C5 3.346 6.346 2 8 2C9.654 2 11 3.346 11 5V6H5V5ZM8 12C7.172 12 6.5 11.328 6.5 10.5C6.5 9.672 7.172 9 8 9C8.828 9 9.5 9.672 9.5 10.5C9.5 11.328 8.828 12 8 12Z" fill="#4CAF50"/>
                          </svg>
                          <span>Secure payment processing</span>
                        </div>
                      </div>
                    </div>
                  </form>
                )}
              </div>

              <div className={styles.orderSummary}>
                <h2 className={styles.summaryTitle}>Order Summary</h2>

                <div className={styles.cartItems}>
                  {cart.map(item => (
                    <div key={item.id} className={styles.cartItem}>
                      <div className={styles.cartItemImage}>
                        <img src={item.image} alt={item.name} />
                      </div>
                      <div className={styles.cartItemDetails}>
                        <h3 className={styles.cartItemName}>{item.name}</h3>
                        <div className={styles.cartItemPrice}>
                          <span>${item.price.toFixed(2)}</span>
                          <span>x {item.quantity}</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                <div className={styles.summaryDetails}>
                  <div className={styles.summaryRow}>
                    <span>Subtotal</span>
                    <span>${orderSummary.subtotal.toFixed(2)}</span>
                  </div>
                  <div className={styles.summaryRow}>
                    <span>Shipping</span>
                    <span>${orderSummary.shipping.toFixed(2)}</span>
                  </div>
                  <div className={`${styles.summaryRow} ${styles.summaryTotal}`}>
                    <span>Total</span>
                    <span>${orderSummary.total.toFixed(2)}</span>
                  </div>
                </div>

                <div className={styles.policyLinks}>
                  <Link href="/policies#shipping-info">Shipping Information</Link>
                  <Link href="/policies#return-policy">Return & Refund Policy</Link>
                </div>

                <div className={styles.secureCheckout}>
                  <div className={styles.secureCheckoutHeader}>
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M8 0C5.243 0 3 2.243 3 5V6H2C1.447 6 1 6.447 1 7V15C1 15.553 1.447 16 2 16H14C14.553 16 15 15.553 15 15V7C15 6.447 14.553 6 14 6H13V5C13 2.243 10.757 0 8 0ZM5 5C5 3.346 6.346 2 8 2C9.654 2 11 3.346 11 5V6H5V5ZM8 12C7.172 12 6.5 11.328 6.5 10.5C6.5 9.672 7.172 9 8 9C8.828 9 9.5 9.672 9.5 10.5C9.5 11.328 8.828 12 8 12Z" fill="#4CAF50"/>
                    </svg>
                    <span>Secure Checkout</span>
                  </div>

                  <div className={styles.paymentLogos}>
                    <img src="/images/logos/square.png" alt="Square Payments" width={80} height={20} />
                  </div>
                </div>
              </div>
            </div>
          </AnimatedSection>
        </section>
      </main>
    </Layout>
  );
}

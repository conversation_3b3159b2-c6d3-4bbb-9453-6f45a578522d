import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import ProtectedRoute from '@/components/ProtectedRoute'
import AdminLayout from '@/components/admin/AdminLayout'
import AvailabilityCalendarCard from '@/components/admin/AvailabilityCalendarCard'
import PerformanceMetricsCard from '@/components/admin/PerformanceMetricsCard'
import QuickActionsCard from '@/components/admin/QuickActionsCard'
import { toast } from 'react-toastify'
import styles from '@/styles/admin/ArtistBraiderDashboard.module.css'

export default function ArtistBraiderDashboard() {
  const { user } = useAuth()
  const [profile, setProfile] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  useEffect(() => {
    fetchDashboardData()
  }, [])

  const fetchDashboardData = async () => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch('/api/artist/dashboard-enhanced', {
        headers: {
          'Authorization': `Bear<PERSON> ${user?.access_token}`,
          'Content-Type': 'application/json'
        }
      })

      if (response.ok) {
        const data = await response.json()
        setProfile(data.profile)
      } else if (response.status === 403) {
        setError('Access denied. This dashboard is only available for artists and braiders.')
      } else {
        throw new Error('Failed to fetch dashboard data')
      }
    } catch (error) {
      console.error('Error fetching dashboard data:', error)
      setError('Failed to load dashboard data. Please try again.')
      toast.error('Failed to load dashboard data')
    } finally {
      setLoading(false)
    }
  }

  const handleProfileUpdate = () => {
    fetchDashboardData()
  }

  const handleAvailabilityUpdate = (newStatus) => {
    setProfile(prev => ({
      ...prev,
      is_available_today: newStatus === 'available'
    }))
  }

  if (loading) {
    return (
      <ProtectedRoute>
        <AdminLayout>
          <div className={styles.container}>
            <div className={styles.header}>
              <h1>Artist & Braider Dashboard</h1>
              <p>Loading your dashboard...</p>
            </div>
            <div className={styles.loadingContainer}>
              <div className={styles.spinner}></div>
              <span>Loading dashboard data...</span>
            </div>
          </div>
        </AdminLayout>
      </ProtectedRoute>
    )
  }

  if (error) {
    return (
      <ProtectedRoute>
        <AdminLayout>
          <div className={styles.container}>
            <div className={styles.header}>
              <h1>Artist & Braider Dashboard</h1>
            </div>
            <div className={styles.errorContainer}>
              <div className={styles.errorIcon}>⚠️</div>
              <h2>Access Restricted</h2>
              <p>{error}</p>
              <button 
                onClick={fetchDashboardData}
                className={styles.retryButton}
              >
                Try Again
              </button>
            </div>
          </div>
        </AdminLayout>
      </ProtectedRoute>
    )
  }

  return (
    <ProtectedRoute>
      <AdminLayout>
        <div className={styles.container}>
          <div className={styles.header}>
            <div className={styles.headerContent}>
              <h1>Artist & Braider Dashboard</h1>
              <p>Welcome back, {profile?.display_name || profile?.name || 'Artist'}!</p>
            </div>
            <div className={styles.headerActions}>
              <button 
                onClick={fetchDashboardData}
                className={styles.refreshButton}
                title="Refresh Dashboard"
              >
                🔄 Refresh
              </button>
            </div>
          </div>

          <div className={styles.dashboardGrid}>
            {/* Quick Actions Card */}
            <div className={styles.quickActionsSection}>
              <QuickActionsCard 
                profile={profile}
                onProfileUpdate={handleProfileUpdate}
              />
            </div>

            {/* Availability Calendar Card */}
            <div className={styles.availabilitySection}>
              <AvailabilityCalendarCard 
                profile={profile}
                onAvailabilityUpdate={handleAvailabilityUpdate}
              />
            </div>

            {/* Performance Metrics Card */}
            <div className={styles.metricsSection}>
              <PerformanceMetricsCard 
                profile={profile}
              />
            </div>
          </div>

          {/* Profile Summary */}
          <div className={styles.profileSummary}>
            <div className={styles.summaryCard}>
              <h3>Profile Summary</h3>
              <div className={styles.summaryGrid}>
                <div className={styles.summaryItem}>
                  <span className={styles.summaryLabel}>Role</span>
                  <span className={styles.summaryValue}>
                    {profile?.role?.charAt(0).toUpperCase() + profile?.role?.slice(1) || 'Artist'}
                  </span>
                </div>
                <div className={styles.summaryItem}>
                  <span className={styles.summaryLabel}>Specialties</span>
                  <span className={styles.summaryValue}>
                    {profile?.specialties?.join(', ') || 'Face Painting, Glitter Art'}
                  </span>
                </div>
                <div className={styles.summaryItem}>
                  <span className={styles.summaryLabel}>Experience</span>
                  <span className={styles.summaryValue}>
                    {profile?.experience_years || 2} years
                  </span>
                </div>
                <div className={styles.summaryItem}>
                  <span className={styles.summaryLabel}>Status</span>
                  <span 
                    className={styles.summaryValue}
                    style={{ 
                      color: profile?.is_available_today ? '#10b981' : '#ef4444',
                      fontWeight: 'bold'
                    }}
                  >
                    {profile?.is_available_today ? 'Available Today' : 'Unavailable Today'}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Help Section */}
          <div className={styles.helpSection}>
            <div className={styles.helpCard}>
              <h3>Need Help?</h3>
              <div className={styles.helpLinks}>
                <a href="/help/artist-guide" target="_blank" className={styles.helpLink}>
                  📖 Artist Guide
                </a>
                <a href="/help/booking-management" target="_blank" className={styles.helpLink}>
                  📅 Booking Management
                </a>
                <a href="/help/contact" target="_blank" className={styles.helpLink}>
                  💬 Contact Support
                </a>
                <a href="/admin/user-management" className={styles.helpLink}>
                  👤 Update Profile
                </a>
              </div>
            </div>
          </div>
        </div>
      </AdminLayout>
    </ProtectedRoute>
  )
}

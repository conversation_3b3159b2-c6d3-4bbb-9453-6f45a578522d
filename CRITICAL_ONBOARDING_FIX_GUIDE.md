# 🚨 Critical Artist/Braider Onboarding Issues - Production Fix Guide

## **Issues Identified & Fixed**

### **Issue 1: Database Relationship Error** ✅ FIXED
**Error:** `PGRST200: Could not find a relationship between 'application_tokens' and 'user_profiles'`

**Root Cause:** Missing or misconfigured foreign key relationships between tables

**Fix Applied:**
- ✅ Corrected foreign key constraints to reference `auth.users` instead of `user_profiles`
- ✅ Added proper indexes for performance
- ✅ Updated Row Level Security policies
- ✅ Created safe token validation functions

### **Issue 2: Nodemailer Import Error** ✅ FIXED
**Error:** `(intermediate value).default.createTransporter is not a function`

**Root Cause:** ES module compatibility issue in production environment

**Fix Applied:**
- ✅ Enhanced import handling for both default and named exports
- ✅ Added fallback mechanisms for different module formats
- ✅ Improved error handling and timeout configurations

---

## **🔧 Immediate Deployment Steps**

### **Step 1: Apply Database Fixes**
```sql
-- Run this in Supabase SQL Editor
-- File: supabase/migrations/fix_application_tokens_relationships.sql
```

1. **Open Supabase Dashboard** → SQL Editor
2. **Copy and paste** the entire contents of `supabase/migrations/fix_application_tokens_relationships.sql`
3. **Execute the SQL** to fix all relationship issues
4. **Verify success** by checking that no errors are returned

### **Step 2: Deploy Code Fixes**
```bash
# Deploy the updated email service manager
git add lib/email-service-manager.js
git commit -m "Fix: Resolve nodemailer import issues for production"
git push origin main
```

### **Step 3: Configure Email Services**
1. **Access Admin Panel** → `/admin/secure-settings`
2. **Configure at least one email service:**

#### **Option A: Gmail SMTP (Recommended)**
- Gmail Email: `<EMAIL>`
- Gmail App Password: [Generate from Google Account Settings]
- Test the connection before saving

#### **Option B: Google Workspace SMTP**
- Workspace Email: `<EMAIL>`
- Workspace App Password: [Generate from Workspace Settings]
- Test the connection before saving

#### **Option C: OneSignal**
- OneSignal App ID: [From OneSignal Dashboard]
- OneSignal REST API Key: [From OneSignal Dashboard]
- Test the connection before saving

### **Step 4: Test the Complete System**
```bash
# Run the comprehensive test script
node scripts/fix-critical-onboarding-issues.js
```

---

## **🧪 Testing Checklist**

### **Database Relationship Tests**
- [ ] Token creation works without foreign key errors
- [ ] Token retrieval works for existing users
- [ ] Application creation links properly to tokens
- [ ] RLS policies allow proper access

### **Email Service Tests**
- [ ] Gmail SMTP sends test emails successfully
- [ ] Workspace SMTP sends test emails successfully
- [ ] OneSignal sends notifications successfully
- [ ] Error handling works for failed services

### **End-to-End Onboarding Tests**
- [ ] Create new Artist user through admin panel
- [ ] Verify welcome email is sent
- [ ] Test application token link access
- [ ] Complete application form submission
- [ ] Verify admin notification emails

---

## **🔍 Troubleshooting Guide**

### **If Database Issues Persist:**
```sql
-- Check foreign key constraints
SELECT 
  tc.table_name, 
  kcu.column_name, 
  ccu.table_name AS foreign_table_name,
  ccu.column_name AS foreign_column_name 
FROM information_schema.table_constraints AS tc 
JOIN information_schema.key_column_usage AS kcu
  ON tc.constraint_name = kcu.constraint_name
JOIN information_schema.constraint_column_usage AS ccu
  ON ccu.constraint_name = tc.constraint_name
WHERE tc.constraint_type = 'FOREIGN KEY' 
  AND tc.table_name IN ('application_tokens', 'artist_braider_applications');
```

### **If Email Services Still Fail:**
1. **Check Environment Variables:**
   ```bash
   # Verify these are set in Vercel
   NEXT_PUBLIC_SUPABASE_URL
   SUPABASE_SERVICE_ROLE_KEY
   ENCRYPTION_KEY
   ```

2. **Test Email Configuration:**
   ```javascript
   // In browser console on /admin/secure-settings
   fetch('/api/admin/secure-settings/test', {
     method: 'POST',
     headers: { 'Content-Type': 'application/json' },
     body: JSON.stringify({
       serviceType: 'gmail',
       credentials: { /* your credentials */ },
       testEmail: '<EMAIL>'
     })
   }).then(r => r.json()).then(console.log)
   ```

### **If Nodemailer Import Still Fails:**
1. **Check Node.js Version** (should be 18.x or higher)
2. **Verify Package.json** includes nodemailer dependency
3. **Check Vercel Build Logs** for import errors

---

## **📊 Success Verification**

### **Database Health Check**
```sql
-- Verify token table structure
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'application_tokens';

-- Test token generation
SELECT generate_application_token();

-- Test token validation
SELECT * FROM validate_application_token('test-token');
```

### **Email Service Health Check**
```bash
# Test all email services
curl -X POST https://your-domain.com/api/admin/secure-settings/test \
  -H "Content-Type: application/json" \
  -d '{"serviceType": "gmail", "testEmail": "<EMAIL>"}'
```

### **Application Flow Health Check**
1. **Create Artist User** → Should generate token
2. **Send Welcome Email** → Should deliver successfully
3. **Access Application Link** → Should validate token
4. **Submit Application** → Should create database entry
5. **Admin Notification** → Should send to admins

---

## **🎯 Expected Results After Fix**

### **Database Operations**
- ✅ Token creation: `✅ Stored application token for artist user`
- ✅ Token retrieval: Returns tokens without PGRST200 errors
- ✅ Application linking: Proper foreign key relationships

### **Email Operations**
- ✅ Gmail SMTP: `✅ Email sent via gmail-smtp`
- ✅ Workspace SMTP: `✅ Email sent via workspace-smtp`
- ✅ OneSignal: `✅ Notification sent via onesignal`

### **User Experience**
- ✅ Artists/Braiders receive welcome emails immediately
- ✅ Application links work without token validation errors
- ✅ Form submissions complete successfully
- ✅ Admin notifications are delivered

---

## **🚀 Post-Fix Monitoring**

### **Key Metrics to Watch**
- **Token Generation Success Rate:** Should be 100%
- **Email Delivery Success Rate:** Should be >95%
- **Application Completion Rate:** Should improve significantly
- **Error Logs:** Should show no PGRST200 or nodemailer errors

### **Monitoring Commands**
```bash
# Check recent application tokens
supabase sql --query "SELECT * FROM application_tokens ORDER BY created_at DESC LIMIT 10;"

# Check recent applications
supabase sql --query "SELECT * FROM artist_braider_applications ORDER BY created_at DESC LIMIT 10;"

# Monitor email service usage
grep "Email sent via" /var/log/vercel.log | tail -20
```

---

## **📞 Support & Escalation**

If issues persist after applying these fixes:

1. **Check Vercel Deployment Logs** for build/runtime errors
2. **Review Supabase Logs** for database operation failures
3. **Test in Development Environment** to isolate production-specific issues
4. **Verify Environment Variables** are properly set in Vercel
5. **Check Email Service Provider Status** (Gmail, Workspace, OneSignal)

**Critical Success Indicators:**
- ✅ No PGRST200 errors in logs
- ✅ No nodemailer import errors
- ✅ Welcome emails delivered within 30 seconds
- ✅ Application tokens validate successfully
- ✅ Complete onboarding flow works end-to-end

---

**🎉 Once these fixes are applied, the Artist/Braider onboarding system should be fully operational in production!**

import { getAdminClient } from '@/lib/supabase';
import { authenticateAdminRequest } from '@/lib/admin-auth';
import { setCacheHeaders } from '@/lib/cache-control-utils';

/**
 * API endpoint for admin settings management
 * This endpoint uses service_role key to bypass RLS policies
 *
 * @param {Object} req - HTTP request object
 * @param {Object} res - HTTP response object
 * @returns {Object} - JSON response
 */
export default async function handler(req, res) {
  // Generate a unique request ID for tracking
  const requestId = Math.random().toString(36).substring(2, 8);
  console.log(`[${requestId}] Settings API endpoint called: ${req.method} ${req.url}`);

  // Log headers for debugging
  console.log(`[${requestId}] Request headers:`, Object.keys(req.headers));
  if (req.headers.authorization) {
    console.log(`[${requestId}] Authorization header present`);
  }
  if (req.headers.cookie) {
    console.log(`[${requestId}] Cookie header present`);
  }
  if (req.headers['x-auth-token'] || req.headers['X-Auth-Token']) {
    console.log(`[${requestId}] Auth token header present`);
  }

  // Authenticate request using our robust auth module
  const authResult = await authenticateAdminRequest(req);
  const { authorized, error, user, role } = authResult;

  if (!authorized) {
    console.error(`[${requestId}] Authentication failed:`, error?.message || 'Unknown error');
    return res.status(401).json({
      error: 'Unauthorized access',
      message: error?.message || 'Authentication failed',
      requestId
    });
  }

  // Only allow admin-level users (dev and admin) to access settings
  const adminRoles = ['dev', 'admin'];
  if (!adminRoles.includes(role)) {
    console.error(`[${requestId}] User ${user?.email} with role ${role} attempted to access admin settings`);
    return res.status(403).json({
      error: 'Forbidden',
      message: 'Only admin-level users can access settings',
      requestId
    });
  }

  console.log(`[${requestId}] Authentication successful. User: ${user?.email}, Role: ${role}`);

  // Check if the request method is allowed
  if (!['GET', 'PUT'].includes(req.method)) {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // GET - Fetch settings
    if (req.method === 'GET') {
      console.log(`[${requestId}] Processing GET request for settings`);

      // Default settings to return if database operations fail
      const defaultSettings = {
        site_name: 'Ocean Soul Sparkles',
        site_description: 'Face painting and body art services',
        contact_email: '<EMAIL>',
        contact_phone: '',
        business_hours: 'Mon-Fri: 9am-5pm, Sat: 10am-3pm, Sun: Closed',
        booking_lead_time: '24',
        booking_max_days_ahead: '60',
        enable_online_bookings: 'true',
        enable_online_payments: 'true',
        theme_primary_color: '#3788d8',
        theme_secondary_color: '#2c3e50',
        theme_accent_color: '#e74c3c',
      };

      // In development mode with auth bypass, return default settings immediately
      if (process.env.NODE_ENV === 'development' && process.env.ENABLE_AUTH_BYPASS === 'true') {
        console.log(`[${requestId}] Development mode: returning default settings`);
        return res.status(200).json({
          ...defaultSettings,
          _note: 'Development mode - using default settings',
          requestId,
          timestamp: new Date().toISOString()
        });
      }

      let adminClient;
      try {
        // Get admin client with shorter timeout to fail fast
        adminClient = getAdminClient();

        if (!adminClient) {
          console.warn(`[${requestId}] Admin client not available, using default settings`);
          return res.status(200).json({
            ...defaultSettings,
            _note: 'Database unavailable - using default settings',
            requestId,
            timestamp: new Date().toISOString()
          });
        }

        console.log(`[${requestId}] Admin client obtained successfully`);
      } catch (adminClientError) {
        console.error(`[${requestId}] Admin client fetch error:`, adminClientError.message);
        console.log(`[${requestId}] Falling back to default settings`);
        return res.status(200).json({
          ...defaultSettings,
          _note: 'Database connection failed - using default settings',
          requestId,
          timestamp: new Date().toISOString()
        });
      }

      try {
        // Try to fetch settings with a simple query first
        let settingsData = null;
        let queryError = null;

        try {
          // Set a shorter timeout for database operations
          const queryPromise = adminClient
            .from('settings')
            .select('*');

          // Use a race condition with timeout to prevent hanging
          const timeoutPromise = new Promise((_, reject) => {
            setTimeout(() => reject(new Error('Query timeout')), 5000); // 5 second timeout
          });

          const result = await Promise.race([queryPromise, timeoutPromise]);
          settingsData = result.data;
          queryError = result.error;
        } catch (dbError) {
          console.warn(`[${requestId}] Database query failed:`, dbError.message);
          queryError = dbError;
        }

        // If query failed or no data, return default settings
        if (queryError || !settingsData) {
          console.log(`[${requestId}] Using default settings due to query failure or missing data`);
          return res.status(200).json({
            ...defaultSettings,
            _note: queryError ? 'Database query failed - using default settings' : 'No settings found - using defaults',
            requestId,
            timestamp: new Date().toISOString()
          });
        }

        // If we have data but it's empty, return default settings
        if (settingsData.length === 0) {
          console.log(`[${requestId}] No settings found in database, returning defaults`);
          return res.status(200).json({
            ...defaultSettings,
            _note: 'No settings configured - using defaults',
            requestId,
            timestamp: new Date().toISOString()
          });
        }

        // Convert array of settings to an object
        const settingsObject = settingsData.reduce((acc, setting) => {
          acc[setting.key] = setting.value;
          return acc;
        }, {});

        // Merge with defaults to ensure all required settings are present
        const finalSettings = { ...defaultSettings, ...settingsObject };

        console.log(`[${requestId}] Successfully fetched ${settingsData.length} settings from database`);

        // Set appropriate cache headers for settings data (static data)
        setCacheHeaders(res, 'settings', 'GET', true, req.query);

        return res.status(200).json({
          ...finalSettings,
          requestId,
          timestamp: new Date().toISOString()
        });

      } catch (queryError) {
        console.error(`[${requestId}] Query execution error:`, queryError.message);
        console.log(`[${requestId}] Falling back to default settings due to query error`);
        return res.status(200).json({
          ...defaultSettings,
          _note: 'Query execution failed - using default settings',
          requestId,
          timestamp: new Date().toISOString()
        });
      }
    }

    // PUT - Update settings
    else if (req.method === 'PUT') {
      console.log(`[${requestId}] Processing PUT request for settings`);

      const { settings } = req.body;

      // Validate settings
      if (!settings || typeof settings !== 'object') {
        return res.status(400).json({
          error: 'Invalid settings format',
          message: 'Settings must be provided as an object',
          requestId
        });
      }

      // In development mode with auth bypass, simulate success
      if (process.env.NODE_ENV === 'development' && process.env.ENABLE_AUTH_BYPASS === 'true') {
        console.log(`[${requestId}] Development mode: simulating settings update`);
        return res.status(200).json({
          success: true,
          updated: Object.keys(settings).length,
          requestId,
          timestamp: new Date().toISOString(),
          _note: 'Development mode - settings update simulated'
        });
      }

      // Get admin client with error handling
      let adminClient;
      try {
        adminClient = getAdminClient();
        if (!adminClient) {
          console.error(`[${requestId}] Admin client not available.`);
          return res.status(503).json({
            error: 'Service temporarily unavailable',
            message: 'Database connection not available',
            requestId
          });
        }
      } catch (adminClientError) {
        console.error(`[${requestId}] Admin client error:`, adminClientError.message);
        return res.status(503).json({
          error: 'Service temporarily unavailable',
          message: 'Database connection failed',
          requestId
        });
      }

      // First check if the settings table exists
      const { data: tableExists, error: tableCheckError } = await adminClient
        .from('information_schema.tables')
        .select('table_name')
        .eq('table_schema', 'public')
        .eq('table_name', 'settings')
        .single();

      // If the table doesn't exist, create it
      if (tableCheckError || !tableExists) {
        console.log(`[${requestId}] settings table does not exist, creating it`);

        // Create the settings table
        const { error: createTableError } = await adminClient.rpc('create_settings_table', {});

        // If there's an error creating the table and it's not because the table already exists
        if (createTableError && !createTableError.message.includes('already exists')) {
          console.error(`[${requestId}] Error creating settings table:`, createTableError);

          // Try a direct SQL approach as fallback
          const { error: sqlError } = await adminClient.rpc('execute_sql', {
            sql: `
              CREATE TABLE IF NOT EXISTS public.settings (
                key TEXT PRIMARY KEY,
                value TEXT NOT NULL,
                created_at TIMESTAMPTZ DEFAULT NOW(),
                updated_at TIMESTAMPTZ DEFAULT NOW()
              );
            `
          });

          if (sqlError) {
            console.error(`[${requestId}] Error creating settings table with SQL:`, sqlError);
            return res.status(500).json({
              error: 'Failed to create settings table',
              message: 'Could not create settings table. Please contact support.',
              requestId
            });
          }
        }
      }

      // Convert settings object to array of upsert operations
      const settingsArray = Object.entries(settings).map(([key, value]) => ({
        key,
        value: typeof value === 'object' ? JSON.stringify(value) : String(value),
        updated_at: new Date().toISOString()
      }));

      // Upsert settings
      const { data, error } = await adminClient
        .from('settings')
        .upsert(settingsArray, { onConflict: 'key' })
        .select();

      if (error) {
        console.error(`[${requestId}] Error updating settings:`, error);
        throw error;
      }

      console.log(`[${requestId}] Successfully updated ${data?.length || 0} settings`);

      // Set no-cache headers for PUT operations
      setCacheHeaders(res, 'settings', 'PUT', true, req.query);

      return res.status(200).json({
        success: true,
        updated: data?.length || 0,
        requestId,
        timestamp: new Date().toISOString()
      });
    }
  } catch (error) {
    console.error(`[${requestId}] Settings API Error:`, error);

    // Determine the appropriate status code based on the error
    let statusCode = 500;
    let errorMessage = 'Failed to process settings request';

    if (error.message && error.message.includes('timeout')) {
      statusCode = 504; // Gateway Timeout
      errorMessage = 'Request timed out while processing';
    } else if (error.message && error.message.includes('not found')) {
      statusCode = 404; // Not Found
      errorMessage = 'Requested resource not found';
    } else if (error.message && (
      error.message.includes('permission') ||
      error.message.includes('access') ||
      error.message.includes('unauthorized')
    )) {
      statusCode = 403; // Forbidden
      errorMessage = 'Permission denied';
    } else if (error.message && error.message.includes('validation')) {
      statusCode = 400; // Bad Request
      errorMessage = 'Validation error';
    }

    return res.status(statusCode).json({
      error: errorMessage,
      message: process.env.NODE_ENV === 'development' ? error.message : 'An error occurred while processing your request',
      requestId,
      timestamp: new Date().toISOString(),
      details: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
}

import { supabaseAdmin } from '@/lib/supabase'
import { authenticateAdminRequest } from '@/lib/admin-auth'

/**
 * API endpoint for creating POS bookings with customer and payment data
 * This endpoint handles the complete POS transaction flow
 *
 * @param {Object} req - HTTP request object
 * @param {Object} res - HTTP response object
 * @returns {Object} - JSON response
 */
export default async function handler(req, res) {
  // Generate a unique request ID for tracking
  const requestId = Math.random().toString(36).substring(2, 8)
  console.log(`[${requestId}] POS Create Booking API called: ${req.method}`)

  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  // Authenticate request
  const authResult = await authenticateAdminRequest(req)
  const { authorized, error, user, role } = authResult

  if (!authorized) {
    console.error(`[${requestId}] Authentication failed:`, error?.message || 'Unknown error')
    return res.status(401).json({
      error: 'Unauthorized access',
      message: error?.message || 'Authentication failed',
      requestId
    })
  }

  console.log(`[${requestId}] Authentication successful. User: ${user?.email}, Role: ${role}`)

  try {
    const { service, artist, tier, timeSlot, customer, payment } = req.body

    // Validate required data
    if (!service?.id || !artist?.id || !tier?.id || !timeSlot?.time || !customer || !payment) {
      return res.status(400).json({
        error: 'Missing required data',
        message: 'Service, artist, tier, timeSlot, customer, and payment information are required'
      })
    }

    console.log(`[${requestId}] Creating POS booking for service: ${service.name}, artist: ${artist.name}`)

    // Start a transaction-like process
    let customerId = null
    let bookingId = null
    let paymentId = null

    try {
      // Generate a unique POS session ID for tracking
      const posSessionId = `pos_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`

      // Step 1: Create or find customer with enhanced error handling
      if (customer.isAnonymous) {
        // For anonymous customers, create a minimal record with new schema
        try {
          const { data: customerData, error: customerError } = await supabaseAdmin
            .from('customers')
            .insert([{
              name: customer.name || `Walk-in Customer #${Date.now()}`,
              email: null, // Now allowed due to schema fix
              phone: customer.phone || null,
              marketing_consent: false,
              notes: 'POS walk-in customer',
              is_anonymous: true,
              pos_customer: true,
              customer_type: 'walk_in'
            }])
            .select()
            .single()

          if (customerError) {
            console.error(`[${requestId}] Error creating anonymous customer:`, customerError)
            throw new Error(`Failed to create anonymous customer: ${customerError.message}`)
          }

          customerId = customerData.id
          console.log(`[${requestId}] Created anonymous customer: ${customerId}`)
        } catch (anonymousError) {
          console.error(`[${requestId}] Anonymous customer creation failed:`, anonymousError)
          throw new Error(`Anonymous customer creation failed: ${anonymousError.message}`)
        }
      } else {
        // For named customers, create or update record with better error handling
        try {
          // Only search by email if email is provided
          let existingCustomer = null
          if (customer.email) {
            const { data } = await supabaseAdmin
              .from('customers')
              .select('id, name, email, phone')
              .eq('email', customer.email)
              .single()
            existingCustomer = data
          }

          if (existingCustomer) {
            customerId = existingCustomer.id
            console.log(`[${requestId}] Using existing customer: ${customerId} (${existingCustomer.email})`)
          } else {
            const { data: customerData, error: customerError } = await supabaseAdmin
              .from('customers')
              .insert([{
                name: customer.name,
                email: customer.email || null, // Now allowed to be null
                phone: customer.phone || null,
                marketing_consent: customer.marketingConsent || false,
                notes: 'Created via POS terminal',
                is_anonymous: false,
                pos_customer: true,
                customer_type: 'regular'
              }])
              .select()
              .single()

            if (customerError) {
              console.error(`[${requestId}] Error creating named customer:`, customerError)
              throw new Error(`Failed to create customer: ${customerError.message}`)
            }

            customerId = customerData.id
            console.log(`[${requestId}] Created new customer: ${customerId} (${customer.email || 'no email'})`)
          }
        } catch (namedError) {
          console.error(`[${requestId}] Named customer creation failed:`, namedError)
          throw new Error(`Customer creation failed: ${namedError.message}`)
        }
      }

      // Step 2: Create booking with enhanced POS tracking
      const startTime = new Date(timeSlot.time)
      const endTime = new Date(startTime.getTime() + (tier.duration * 60 * 1000))

      try {
        const { data: bookingData, error: bookingError } = await supabaseAdmin
          .from('bookings')
          .insert([{
            customer_id: customerId,
            service_id: service.id,
            artist_id: artist.id,
            start_time: startTime.toISOString(),
            end_time: endTime.toISOString(),
            status: 'confirmed', // POS bookings are immediately confirmed
            location: 'Festival/Event Location',
            notes: `POS booking - ${tier.name} tier - ${payment.method} payment - Artist: ${artist.name}`,
            booking_source: 'pos',
            pos_session_id: posSessionId,
            tier_name: tier.name,
            tier_price: tier.price,
            total_amount: payment.amount
          }])
          .select()
          .single()

        if (bookingError) {
          console.error(`[${requestId}] Error creating booking:`, bookingError)
          throw new Error(`Failed to create booking: ${bookingError.message}`)
        }

        bookingId = bookingData.id
        console.log(`[${requestId}] Created POS booking: ${bookingId} for session: ${posSessionId}`)
      } catch (bookingCreationError) {
        console.error(`[${requestId}] Booking creation failed:`, bookingCreationError)
        throw new Error(`Booking creation failed: ${bookingCreationError.message}`)
      }

      // Step 3: Create payment record with enhanced POS tracking
      try {
        const { data: paymentData, error: paymentError } = await supabaseAdmin
          .from('payments')
          .insert([{
            booking_id: bookingId,
            amount: payment.amount,
            currency: payment.currency || 'AUD',
            payment_method: payment.method,
            payment_status: 'completed',
            transaction_id: payment.details?.transactionId || null,
            payment_date: new Date().toISOString(),
            notes: `POS terminal payment - ${service.name} (${tier.name})`,
            pos_session_id: posSessionId,
            square_payment_id: payment.details?.paymentId || null,
            receipt_url: payment.details?.receiptUrl || null
          }])
          .select()
          .single()

        if (paymentError) {
          console.error(`[${requestId}] Error creating payment:`, paymentError)
          throw new Error(`Failed to create payment record: ${paymentError.message}`)
        }

        paymentId = paymentData.id
        console.log(`[${requestId}] Created POS payment: ${paymentId} for session: ${posSessionId}`)
      } catch (paymentCreationError) {
        console.error(`[${requestId}] Payment creation failed:`, paymentCreationError)
        throw new Error(`Payment creation failed: ${paymentCreationError.message}`)
      }

      // Return success response
      return res.status(201).json({
        success: true,
        booking: {
          id: bookingId,
          customer_id: customerId,
          service_id: service.id,
          start_time: startTime.toISOString(),
          end_time: endTime.toISOString(),
          status: 'confirmed'
        },
        payment: {
          id: paymentId,
          amount: payment.amount,
          currency: payment.currency || 'AUD',
          method: payment.method,
          status: 'completed'
        },
        customer: {
          id: customerId,
          name: customer.name,
          email: customer.email || null
        },
        receipt: {
          service: service.name,
          tier: tier.name,
          duration: tier.duration,
          amount: payment.amount,
          currency: payment.currency || 'AUD',
          payment_method: payment.method,
          booking_time: startTime.toISOString(),
          booking_id: bookingId
        }
      })

    } catch (transactionError) {
      console.error(`[${requestId}] Transaction error:`, transactionError)

      // Attempt cleanup if we created partial records
      if (paymentId) {
        await supabaseAdmin.from('payments').delete().eq('id', paymentId)
      }
      if (bookingId) {
        await supabaseAdmin.from('bookings').delete().eq('id', bookingId)
      }
      if (customerId && customer.isAnonymous) {
        await supabaseAdmin.from('customers').delete().eq('id', customerId)
      }

      throw transactionError
    }

  } catch (error) {
    console.error(`[${requestId}] POS Booking Creation Error:`, error)

    // Determine appropriate status code
    let statusCode = 500
    let errorMessage = 'Failed to create booking'

    if (error.message && error.message.includes('validation')) {
      statusCode = 400
      errorMessage = 'Invalid booking data'
    } else if (error.message && error.message.includes('not found')) {
      statusCode = 404
      errorMessage = 'Service or tier not found'
    }

    return res.status(statusCode).json({
      success: false,
      error: errorMessage,
      message: process.env.NODE_ENV === 'development' ? error.message : 'An error occurred while creating the booking',
      requestId,
      timestamp: new Date().toISOString()
    })
  }
}

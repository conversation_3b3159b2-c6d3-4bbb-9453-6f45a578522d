import { useState } from 'react';
import styles from '@/styles/PaymentMethodSelector.module.css';
import Image from 'next/image';

/**
 * PaymentMethodSelector component for selecting payment method
 * 
 * @param {Object} props - Component props
 * @param {Function} props.onSelectMethod - Function to call when payment method is selected
 * @param {string} props.selectedMethod - Currently selected payment method
 * @returns {JSX.Element}
 */
const PaymentMethodSelector = ({ onSelectMethod, selectedMethod }) => {
  const handleMethodChange = (method) => {
    onSelectMethod(method);
  };
  
  return (
    <div className={styles.paymentMethodSelector}>
      <h3 className={styles.sectionTitle}>Select Payment Method</h3>
      
      <div className={styles.paymentOptions}>
        <div className={`${styles.paymentOption} ${styles.selected}`}>
          <div className={styles.radioButton}>
            <div className={styles.radioSelected}></div>
          </div>
          <div className={styles.paymentLogo}>
            <img
              src="/images/logos/square.png"
              alt="Square Payments"
              width={120}
              height={32}
            />
          </div>
        </div>
      </div>
      
      <div className={styles.securePaymentNote}>
        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M8 0C5.243 0 3 2.243 3 5V6H2C1.447 6 1 6.447 1 7V15C1 15.553 1.447 16 2 16H14C14.553 16 15 15.553 15 15V7C15 6.447 14.553 6 14 6H13V5C13 2.243 10.757 0 8 0ZM5 5C5 3.346 6.346 2 8 2C9.654 2 11 3.346 11 5V6H5V5ZM8 12C7.172 12 6.5 11.328 6.5 10.5C6.5 9.672 7.172 9 8 9C8.828 9 9.5 9.672 9.5 10.5C9.5 11.328 8.828 12 8 12Z" fill="#4CAF50"/>
        </svg>
        <span>Secure payment processing with Square</span>
      </div>
    </div>
  );
};

export default PaymentMethodSelector;

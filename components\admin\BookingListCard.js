import { useState } from 'react'
import styles from '@/styles/admin/ArtistBraiderDashboard.module.css'

export default function BookingListCard({ bookings, onBookingAction }) {
  const [filter, setFilter] = useState('upcoming') // 'upcoming', 'today', 'completed'

  const formatDateTime = (dateTime) => {
    return new Date(dateTime).toLocaleString('en-AU', {
      weekday: 'short',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const formatCurrency = (amount) => {
    return `$${parseFloat(amount || 0).toFixed(2)}`
  }

  const getFilteredBookings = () => {
    if (!bookings) return []
    
    const now = new Date()
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
    
    switch (filter) {
      case 'today':
        return bookings.filter(booking => {
          const bookingDate = new Date(booking.start_time)
          return bookingDate >= today && bookingDate < new Date(today.getTime() + 24 * 60 * 60 * 1000)
        })
      case 'completed':
        return bookings.filter(booking => booking.status === 'completed')
      case 'upcoming':
      default:
        return bookings.filter(booking => 
          new Date(booking.start_time) > now && booking.status !== 'cancelled'
        )
    }
  }

  const filteredBookings = getFilteredBookings()

  const handleBookingAction = (bookingId, action) => {
    if (onBookingAction) {
      onBookingAction(bookingId, action)
    }
  }

  return (
    <div className={styles.dashboardCard}>
      <div className={styles.cardHeader}>
        <h3>Bookings</h3>
        <div className={styles.filterTabs}>
          <button
            className={filter === 'upcoming' ? styles.activeTab : styles.tab}
            onClick={() => setFilter('upcoming')}
          >
            Upcoming
          </button>
          <button
            className={filter === 'today' ? styles.activeTab : styles.tab}
            onClick={() => setFilter('today')}
          >
            Today
          </button>
          <button
            className={filter === 'completed' ? styles.activeTab : styles.tab}
            onClick={() => setFilter('completed')}
          >
            Completed
          </button>
        </div>
      </div>

      <div className={styles.cardContent}>
        {filteredBookings.length === 0 ? (
          <div className={styles.emptyState}>
            <p>No {filter} bookings found.</p>
          </div>
        ) : (
          <div className={styles.bookingsList}>
            {filteredBookings.map((booking) => (
              <div key={booking.id} className={styles.bookingItem}>
                <div className={styles.bookingInfo}>
                  <h4 className={styles.serviceName}>
                    {booking.service_name || 'Service'}
                  </h4>
                  <p className={styles.customerName}>
                    {booking.customer_name || 'Customer'}
                  </p>
                  <p className={styles.bookingTime}>
                    {formatDateTime(booking.start_time)}
                  </p>
                  <p className={styles.bookingAmount}>
                    {formatCurrency(booking.total_amount)}
                  </p>
                </div>
                
                <div className={styles.bookingActions}>
                  {filter === 'upcoming' && (
                    <>
                      <button
                        className={styles.actionButton}
                        onClick={() => handleBookingAction(booking.id, 'view')}
                      >
                        View
                      </button>
                      <button
                        className={styles.actionButton}
                        onClick={() => handleBookingAction(booking.id, 'reschedule')}
                      >
                        Reschedule
                      </button>
                    </>
                  )}
                  {filter === 'today' && (
                    <>
                      <button
                        className={styles.primaryButton}
                        onClick={() => handleBookingAction(booking.id, 'complete')}
                      >
                        Complete
                      </button>
                      <button
                        className={styles.actionButton}
                        onClick={() => handleBookingAction(booking.id, 'view')}
                      >
                        Details
                      </button>
                    </>
                  )}
                  {filter === 'completed' && (
                    <button
                      className={styles.actionButton}
                      onClick={() => handleBookingAction(booking.id, 'view')}
                    >
                      View
                    </button>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}

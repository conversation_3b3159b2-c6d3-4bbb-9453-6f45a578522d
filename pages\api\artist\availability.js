import { supabase } from '@/lib/supabase'
import { authenticateAdminRequest } from '@/lib/auth'
import { hasBookingPermission, BOOKING_PERMISSIONS } from '@/lib/artist-booking-permissions'

export default async function handler(req, res) {
  const requestId = Math.random().toString(36).substring(2, 8)
  console.log(`[${requestId}] Artist Availability API called: ${req.method}`)

  if (req.method !== 'PUT') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    // Authenticate the request
    const authResult = await authenticateAdminRequest(req, res, requestId)
    if (!authResult.success) {
      return res.status(401).json({ error: 'Authentication failed' })
    }

    const { user, role } = authResult

    // Check if user has permission to manage their own availability
    if (!hasBookingPermission(role, BOOKING_PERMISSIONS.MANAGE_OWN_AVAILABILITY)) {
      console.log(`[${requestId}] Access denied for role: ${role}`)
      return res.status(403).json({ error: 'Access denied. Artist/Braider role required.' })
    }

    const { is_available_today } = req.body

    if (typeof is_available_today !== 'boolean') {
      return res.status(400).json({ error: 'is_available_today must be a boolean value' })
    }

    console.log(`[${requestId}] Updating availability for user: ${user.email} to ${is_available_today}`)

    // Check if artist profile exists
    const { data: existingProfile, error: profileError } = await supabase
      .from('artist_braider_profiles')
      .select('id, is_available_today')
      .eq('id', user.id)
      .single()

    if (profileError && profileError.code !== 'PGRST116') {
      console.error(`[${requestId}] Error checking profile:`, profileError)
      return res.status(500).json({ error: 'Failed to check profile' })
    }

    let result
    if (existingProfile) {
      // Update existing profile
      const { data, error } = await supabase
        .from('artist_braider_profiles')
        .update({ 
          is_available_today,
          updated_at: new Date().toISOString()
        })
        .eq('id', user.id)
        .select()
        .single()

      if (error) {
        console.error(`[${requestId}] Error updating availability:`, error)
        return res.status(500).json({ error: 'Failed to update availability' })
      }

      result = data
      console.log(`[${requestId}] Updated availability for existing profile`)
    } else {
      // Create new profile
      const { data, error } = await supabase
        .from('artist_braider_profiles')
        .insert({
          id: user.id,
          is_available_today,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single()

      if (error) {
        console.error(`[${requestId}] Error creating profile:`, error)
        return res.status(500).json({ error: 'Failed to create profile' })
      }

      result = data
      console.log(`[${requestId}] Created new profile with availability`)
    }

    // Log availability change for audit purposes
    const { error: logError } = await supabase
      .from('availability_logs')
      .insert({
        artist_id: user.id,
        previous_status: existingProfile?.is_available_today || false,
        new_status: is_available_today,
        changed_by: user.id,
        change_reason: 'Manual update via dashboard',
        created_at: new Date().toISOString()
      })

    if (logError) {
      console.warn(`[${requestId}] Failed to log availability change:`, logError)
      // Don't fail the request if logging fails
    }

    console.log(`[${requestId}] Successfully updated availability to: ${is_available_today}`)

    res.status(200).json({
      success: true,
      message: `Availability updated to ${is_available_today ? 'available' : 'unavailable'}`,
      profile: {
        id: result.id,
        is_available_today: result.is_available_today,
        updated_at: result.updated_at
      }
    })

  } catch (error) {
    console.error(`[${requestId}] Error in availability API:`, error)
    res.status(500).json({ error: 'Internal server error' })
  }
}

import { supabase } from '@/lib/supabase'
import { authenticateAdminRequest } from '@/lib/admin-auth'
import { hasBookingPermission, BOOKING_PERMISSIONS } from '@/lib/artist-booking-permissions'

export default async function handler(req, res) {
  const requestId = Math.random().toString(36).substring(2, 8)
  console.log(`[${requestId}] Performance Metrics API called: ${req.method}`)

  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    // Authenticate the request
    const authResult = await authenticateAdminRequest(req, res, requestId)
    if (!authResult.success) {
      return res.status(401).json({ error: 'Authentication failed' })
    }

    const { user, role } = authResult

    // Check if user has permission to view their own earnings
    if (!hasBookingPermission(role, BOOKING_PERMISSIONS.VIEW_OWN_EARNINGS)) {
      console.log(`[${requestId}] Access denied for role: ${role}`)
      return res.status(403).json({ error: 'Access denied. Artist/Braider role required.' })
    }

    console.log(`[${requestId}] Fetching performance metrics for user: ${user.email}`)

    // Get date ranges
    const now = new Date()
    const thisMonthStart = new Date(now.getFullYear(), now.getMonth(), 1)
    const thisMonthEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59)
    const lastMonthStart = new Date(now.getFullYear(), now.getMonth() - 1, 1)
    const lastMonthEnd = new Date(now.getFullYear(), now.getMonth(), 0, 23, 59, 59)

    // Fetch this month's bookings
    const { data: thisMonthBookings, error: thisMonthError } = await supabase
      .from('bookings')
      .select(`
        id,
        status,
        start_time,
        payments:payments(amount, payment_status),
        reviews:reviews(rating)
      `)
      .or(`assigned_artist_id.eq.${user.id},preferred_artist_id.eq.${user.id}`)
      .gte('start_time', thisMonthStart.toISOString())
      .lte('start_time', thisMonthEnd.toISOString())

    if (thisMonthError) {
      console.error(`[${requestId}] Error fetching this month's bookings:`, thisMonthError)
      return res.status(500).json({ error: 'Failed to fetch performance metrics' })
    }

    // Fetch last month's bookings
    const { data: lastMonthBookings, error: lastMonthError } = await supabase
      .from('bookings')
      .select(`
        id,
        status,
        start_time,
        payments:payments(amount, payment_status),
        reviews:reviews(rating)
      `)
      .or(`assigned_artist_id.eq.${user.id},preferred_artist_id.eq.${user.id}`)
      .gte('start_time', lastMonthStart.toISOString())
      .lte('start_time', lastMonthEnd.toISOString())

    if (lastMonthError) {
      console.error(`[${requestId}] Error fetching last month's bookings:`, lastMonthError)
      return res.status(500).json({ error: 'Failed to fetch performance metrics' })
    }

    // Calculate metrics for this month
    const thisMonthMetrics = calculateMetrics(thisMonthBookings)
    const lastMonthMetrics = calculateMetrics(lastMonthBookings)

    // Calculate trends (percentage change)
    const trends = {
      bookings: calculateTrend(lastMonthMetrics.bookings, thisMonthMetrics.bookings),
      revenue: calculateTrend(lastMonthMetrics.revenue, thisMonthMetrics.revenue),
      rating: calculateTrend(lastMonthMetrics.rating, thisMonthMetrics.rating),
      completionRate: calculateTrend(lastMonthMetrics.completionRate, thisMonthMetrics.completionRate)
    }

    console.log(`[${requestId}] Successfully calculated performance metrics`)

    res.status(200).json({
      success: true,
      metrics: {
        thisMonth: thisMonthMetrics,
        lastMonth: lastMonthMetrics,
        trends
      },
      period: {
        thisMonth: {
          start: thisMonthStart.toISOString().split('T')[0],
          end: thisMonthEnd.toISOString().split('T')[0]
        },
        lastMonth: {
          start: lastMonthStart.toISOString().split('T')[0],
          end: lastMonthEnd.toISOString().split('T')[0]
        }
      }
    })

  } catch (error) {
    console.error(`[${requestId}] Error in performance metrics API:`, error)
    res.status(500).json({ error: 'Internal server error' })
  }
}

function calculateMetrics(bookings) {
  const totalBookings = bookings.length
  const completedBookings = bookings.filter(b => b.status === 'completed')
  
  // Calculate revenue from completed bookings with successful payments
  const revenue = bookings.reduce((total, booking) => {
    if (booking.status === 'completed' && booking.payments) {
      const successfulPayments = booking.payments.filter(p => p.payment_status === 'completed')
      return total + successfulPayments.reduce((sum, payment) => sum + (payment.amount || 0), 0)
    }
    return total
  }, 0)

  // Calculate average rating
  const ratingsData = bookings
    .filter(b => b.reviews && b.reviews.length > 0)
    .flatMap(b => b.reviews)
    .map(r => r.rating)
    .filter(r => r !== null && r !== undefined)

  const averageRating = ratingsData.length > 0 
    ? ratingsData.reduce((sum, rating) => sum + rating, 0) / ratingsData.length 
    : 0

  // Calculate completion rate
  const completionRate = totalBookings > 0 
    ? (completedBookings.length / totalBookings) * 100 
    : 0

  return {
    bookings: totalBookings,
    revenue: revenue / 100, // Convert from cents to dollars
    rating: averageRating,
    completionRate
  }
}

function calculateTrend(oldValue, newValue) {
  if (oldValue === 0) {
    return newValue > 0 ? 100 : 0
  }
  return Math.round(((newValue - oldValue) / oldValue) * 100)
}

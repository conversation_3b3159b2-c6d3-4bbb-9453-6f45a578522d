import { supabase } from '@/lib/supabase'
import { authenticateAdminRequest } from '@/lib/admin-auth'
import { hasBookingPermission, BOOKING_PERMISSIONS } from '@/lib/artist-booking-permissions'

export default async function handler(req, res) {
  const requestId = Math.random().toString(36).substring(2, 8)
  console.log(`[${requestId}] Today's Schedule API called: ${req.method}`)

  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    // Authenticate the request
    const authResult = await authenticateAdminRequest(req, res, requestId)
    if (!authResult.success) {
      return res.status(401).json({ error: 'Authentication failed' })
    }

    const { user, role } = authResult

    // Check if user has permission to view their own bookings
    if (!hasBookingPermission(role, BOOKING_PERMISSIONS.VIEW_OWN_BOOKINGS)) {
      console.log(`[${requestId}] Access denied for role: ${role}`)
      return res.status(403).json({ error: 'Access denied. Artist/Braider role required.' })
    }

    // Get today's date range
    const today = new Date()
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate())
    const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1)

    console.log(`[${requestId}] Fetching today's schedule for user: ${user.email}`)
    console.log(`[${requestId}] Date range: ${startOfDay.toISOString()} to ${endOfDay.toISOString()}`)

    // Fetch today's bookings for the artist
    const { data: bookings, error: bookingsError } = await supabase
      .from('bookings')
      .select(`
        id,
        start_time,
        end_time,
        status,
        notes,
        customers:customer_id (
          name,
          email,
          phone
        ),
        services:service_id (
          name,
          color
        )
      `)
      .or(`assigned_artist_id.eq.${user.id},preferred_artist_id.eq.${user.id}`)
      .gte('start_time', startOfDay.toISOString())
      .lt('start_time', endOfDay.toISOString())
      .order('start_time', { ascending: true })

    if (bookingsError) {
      console.error(`[${requestId}] Error fetching bookings:`, bookingsError)
      return res.status(500).json({ error: 'Failed to fetch today\'s schedule' })
    }

    // Format the schedule data
    const schedule = bookings.map(booking => ({
      id: booking.id,
      start_time: booking.start_time,
      end_time: booking.end_time,
      status: booking.status,
      service_name: booking.services?.name || 'Unknown Service',
      service_color: booking.services?.color || '#3b82f6',
      customer_name: booking.customers?.name || 'Unknown Customer',
      customer_email: booking.customers?.email,
      customer_phone: booking.customers?.phone,
      notes: booking.notes
    }))

    console.log(`[${requestId}] Successfully fetched ${schedule.length} bookings for today`)

    res.status(200).json({
      success: true,
      schedule,
      date: today.toISOString().split('T')[0],
      total_bookings: schedule.length
    })

  } catch (error) {
    console.error(`[${requestId}] Error in today's schedule API:`, error)
    res.status(500).json({ error: 'Internal server error' })
  }
}

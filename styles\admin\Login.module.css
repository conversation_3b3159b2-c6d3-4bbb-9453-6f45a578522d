.loginContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #f5f5f5;
  background-image: url('/background.png');
  background-size: cover;
  background-position: center;
  padding: 20px;
}

.loginCard {
  width: 100%;
  max-width: 400px;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  padding: 30px;
}

.logoContainer {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
}

.logo {
  max-width: 150px;
  height: auto;
}

.title {
  text-align: center;
  font-size: 24px;
  margin-bottom: 20px;
  color: #333;
}

.error {
  background-color: #ffebee;
  color: #d32f2f;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 20px;
  font-size: 14px;
  text-align: center;
}

.success {
  background-color: #e8f5e9;
  color: #2e7d32;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 20px;
  font-size: 14px;
  text-align: center;
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20px;
  text-align: center;
  color: #6a0dad;
}

.spinner {
  width: 40px;
  height: 40px;
  margin: 15px auto;
  border: 4px solid rgba(106, 13, 173, 0.2);
  border-top-color: #6a0dad;
  border-radius: 50%;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.formGroup {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.formGroup label {
  font-size: 14px;
  font-weight: 500;
  color: #555;
}

.input {
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 16px;
  transition: border-color 0.2s;
}

.input:focus {
  border-color: #6a0dad;
  outline: none;
}

.forgotPassword {
  text-align: right;
  font-size: 14px;
}

.forgotPassword a, .forgotPassword .link {
  color: #6a0dad;
  text-decoration: none;
}

.forgotPassword a:hover, .forgotPassword .link:hover {
  text-decoration: underline;
}

.loginButton {
  background-color: #6a0dad;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 12px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.loginButton:hover {
  background-color: #5a0b8d;
}

.loginButton:disabled {
  background-color: #9c7fb3;
  cursor: not-allowed;
}

.backToSite {
  margin-top: 20px;
  text-align: center;
  font-size: 14px;
}

.backToSite a, .backToSite .link {
  color: #6a0dad;
  text-decoration: none;
}

.backToSite a:hover, .backToSite .link:hover {
  text-decoration: underline;
}

/* Password Requirements Styles */
.passwordRequirements {
  background: linear-gradient(135deg, #f8f9ff 0%, #e8f4fd 100%);
  border: 1px solid #e0e6ff;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 25px;
}

.passwordRequirements h3 {
  margin: 0 0 10px 0;
  color: #667eea;
  font-size: 18px;
  font-weight: 600;
}

.passwordRequirements p {
  margin: 0 0 15px 0;
  color: #666;
  font-size: 14px;
}

.requirementsList {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.requirement {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.requirement.valid {
  background-color: #e8f5e9;
  color: #2e7d32;
  border: 1px solid #c8e6c9;
}

.requirement.invalid {
  background-color: #fff3e0;
  color: #f57c00;
  border: 1px solid #ffcc02;
}

.requirement .icon {
  font-size: 16px;
  min-width: 20px;
}

/* Enhanced Success Styles */
.success {
  background: linear-gradient(135deg, #e8f5e9 0%, #f1f8e9 100%);
  color: #2e7d32;
  padding: 25px;
  border-radius: 8px;
  margin-bottom: 25px;
  text-align: center;
  border: 1px solid #c8e6c9;
}

.successIcon {
  font-size: 48px;
  margin-bottom: 15px;
  display: block;
}

.success h2 {
  margin: 0 0 15px 0;
  color: #1b5e20;
  font-size: 24px;
  font-weight: 600;
}

.success p {
  margin: 0 0 10px 0;
  font-size: 16px;
  line-height: 1.5;
}

.nextStepsBox {
  background: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
  padding: 20px;
  margin-top: 20px;
  text-align: left;
}

.nextStepsBox h3 {
  margin: 0 0 15px 0;
  color: #1b5e20;
  font-size: 18px;
  font-weight: 600;
}

.nextStepsBox ul {
  margin: 0;
  padding-left: 20px;
}

.nextStepsBox li {
  margin-bottom: 8px;
  color: #2e7d32;
  font-weight: 500;
}

/* Enhanced Button Styles */
.buttonGroup {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-top: 25px;
}

.submitButton {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 15px 20px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.submitButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.submitButton:disabled {
  background: #9c7fb3;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.primaryButton {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.primaryButton:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

.secondaryButton {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.secondaryButton:hover {
  background: linear-gradient(135deg, #e081e9 0%, #e3455a 100%);
}

/* Welcome Message Styles */
.welcomeMessage {
  background: linear-gradient(135deg, #fff3e0 0%, #fce4ec 100%);
  border: 1px solid #ffcc02;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 25px;
  text-align: center;
}

.welcomeMessage p {
  margin: 0 0 10px 0;
  color: #e65100;
  font-weight: 500;
}

.welcomeMessage strong {
  color: #bf360c;
}

/* Input Group Styles */
.inputGroup {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 20px;
}

.inputGroup label {
  font-size: 14px;
  font-weight: 600;
  color: #555;
}

/* Help Text Styles */
.helpText {
  margin-top: 20px;
  text-align: center;
  font-size: 14px;
  color: #666;
}

.helpText a {
  color: #667eea;
  text-decoration: none;
  font-weight: 500;
}

.helpText a:hover {
  text-decoration: underline;
}

@media (max-width: 480px) {
  .loginCard {
    padding: 20px;
    max-width: 350px;
  }

  .title {
    font-size: 20px;
  }

  .input {
    font-size: 14px;
  }

  .submitButton {
    font-size: 14px;
    padding: 12px 16px;
  }

  .passwordRequirements {
    padding: 15px;
  }

  .passwordRequirements h3 {
    font-size: 16px;
  }

  .requirement {
    padding: 6px 10px;
    font-size: 13px;
  }

  .success h2 {
    font-size: 20px;
  }

  .nextStepsBox {
    padding: 15px;
  }

  .buttonGroup {
    gap: 12px;
  }
}

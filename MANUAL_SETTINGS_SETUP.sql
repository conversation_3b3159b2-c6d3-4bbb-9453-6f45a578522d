-- Manual Settings Table Setup for Supabase SQL Editor
-- Copy and paste this entire script into Supabase SQL Editor

-- Step 1: Create the settings table
CREATE TABLE IF NOT EXISTS public.settings (
  key TEXT PRIMARY KEY,
  value TEXT NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Step 2: Enable Row Level Security
ALTER TABLE public.settings ENABLE ROW LEVEL SECURITY;

-- Step 3: Create policies (if user_roles table exists)
-- Note: These might fail if user_roles table doesn't exist - that's okay for now
DO $$ 
BEGIN
  -- Create policy for admin access
  DROP POLICY IF EXISTS "Admin users can manage settings" ON public.settings;
  CREATE POLICY "Admin users can manage settings" 
    ON public.settings 
    USING (
      EXISTS (SELECT 1 FROM public.user_roles WHERE id = auth.uid() AND role = 'admin')
    );
    
  -- Create policy for staff read-only access
  DROP POLICY IF EXISTS "Staff users can read settings" ON public.settings;
  CREATE POLICY "Staff users can read settings" 
    ON public.settings 
    FOR SELECT
    USING (
      EXISTS (SELECT 1 FROM public.user_roles WHERE id = auth.uid() AND role IN ('staff', 'admin'))
    );
EXCEPTION WHEN OTHERS THEN
  -- If user_roles table doesn't exist, create a permissive policy for now
  DROP POLICY IF EXISTS "Allow all authenticated users" ON public.settings;
  CREATE POLICY "Allow all authenticated users" 
    ON public.settings 
    USING (auth.uid() IS NOT NULL);
END $$;

-- Step 4: Insert basic settings
INSERT INTO public.settings (key, value) VALUES
  -- Basic Settings
  ('site_name', 'Ocean Soul Sparkles'),
  ('site_description', 'Face painting and body art services'),
  ('contact_email', '<EMAIL>'),
  ('contact_phone', ''),
  ('business_hours', 'Mon-Fri: 9am-5pm, Sat: 10am-3pm, Sun: Closed'),
  ('booking_lead_time', '24'),
  ('booking_max_days_ahead', '60'),
  ('enable_online_bookings', 'true'),
  ('enable_online_payments', 'true'),
  ('theme_primary_color', '#3788d8'),
  ('theme_secondary_color', '#2c3e50'),
  ('theme_accent_color', '#e74c3c')
ON CONFLICT (key) DO NOTHING;

-- Step 5: Insert enhanced settings for Square API, Google integrations, SEO, email, and security
INSERT INTO public.settings (key, value) VALUES
  -- Square API Settings
  ('square_application_id', ''),
  ('square_access_token', ''),
  ('square_environment', 'sandbox'),
  ('square_location_id', ''),
  ('square_webhook_signature_key', ''),
  ('square_currency', 'AUD'),
  ('enable_square_payments', 'false'),
  
  -- Google Integrations
  ('google_analytics_measurement_id', ''),
  ('google_tag_manager_id', ''),
  ('google_search_console_verification', ''),
  ('google_business_profile_id', ''),
  ('google_my_business_api_key', ''),
  ('google_maps_api_key', ''),
  ('google_ads_customer_id', ''),
  ('google_ads_conversion_id', ''),
  ('enable_google_business_integration', 'false'),
  ('enable_google_reviews_widget', 'false'),
  
  -- SEO Settings
  ('seo_meta_title', ''),
  ('seo_meta_description', ''),
  ('seo_keywords', ''),
  ('seo_canonical_url', ''),
  ('enable_schema_markup', 'true'),
  
  -- Email Settings
  ('smtp_host', ''),
  ('smtp_port', '587'),
  ('smtp_username', ''),
  ('smtp_password', ''),
  ('smtp_encryption', 'tls'),
  ('email_from_address', ''),
  ('email_from_name', ''),
  ('enable_email_notifications', 'true'),
  
  -- Backup & Security
  ('enable_auto_backup', 'false'),
  ('backup_frequency', 'weekly'),
  ('enable_two_factor_auth', 'false'),
  ('session_timeout', '60'),
  
  -- Additional Settings
  ('notification_email', ''),
  ('google_analytics_id', ''),
  ('facebook_pixel_id', ''),
  ('logo_url', ''),
  ('favicon_url', ''),
  ('terms_url', ''),
  ('privacy_url', ''),
  ('social_facebook', ''),
  ('social_instagram', ''),
  ('social_twitter', ''),
  ('social_linkedin', ''),
  ('social_youtube', ''),
  ('custom_css', ''),
  ('custom_js', '')
ON CONFLICT (key) DO NOTHING;

-- Step 6: Create indexes for frequently accessed settings
CREATE INDEX IF NOT EXISTS idx_settings_key ON public.settings(key);

-- Step 7: Create utility functions for settings management
CREATE OR REPLACE FUNCTION public.get_settings(setting_keys text[])
RETURNS TABLE(key text, value text)
LANGUAGE sql
STABLE
AS $$
  SELECT key, value 
  FROM public.settings 
  WHERE key = ANY(setting_keys);
$$;

CREATE OR REPLACE FUNCTION public.update_settings(settings_data jsonb)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  setting_key text;
  setting_value text;
BEGIN
  FOR setting_key, setting_value IN SELECT * FROM jsonb_each_text(settings_data)
  LOOP
    INSERT INTO public.settings (key, value, updated_at)
    VALUES (setting_key, setting_value, NOW())
    ON CONFLICT (key) 
    DO UPDATE SET 
      value = EXCLUDED.value,
      updated_at = EXCLUDED.updated_at;
  END LOOP;
END;
$$;

-- Step 8: Grant necessary permissions
GRANT EXECUTE ON FUNCTION public.get_settings(text[]) TO authenticated;
GRANT EXECUTE ON FUNCTION public.update_settings(jsonb) TO authenticated;

-- Step 9: Add comments
COMMENT ON TABLE public.settings IS 'Application settings for Ocean Soul Sparkles website';
COMMENT ON FUNCTION public.get_settings(text[]) IS 'Get multiple settings by key array';
COMMENT ON FUNCTION public.update_settings(jsonb) IS 'Update multiple settings from JSON object';

-- Verification query - run this to confirm everything worked
SELECT COUNT(*) as total_settings FROM public.settings;
SELECT 'Setup complete!' as status;
